# 購案審查系統環境配置示例
# 複製此文件為 .env 並修改相應的值

# 基本設置
PROJECT_NAME=購案審查系統
VERSION=1.0.0
DEBUG=true

# 服務器設置
HOST=0.0.0.0
PORT=8000

# 安全設置
SECRET_KEY=your-super-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=10080

# CORS 設置
ALLOWED_HOSTS=http://localhost:3000,http://localhost:5173,http://127.0.0.1:3000,http://127.0.0.1:5173

# 數據庫設置
DATABASE_TYPE=sqlite
DATABASE_URL=sqlite:///./purchase_review.db

# PostgreSQL 設置（當 DATABASE_TYPE=postgresql 時使用）
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=purchase_review

# MySQL 設置（當 DATABASE_TYPE=mysql 時使用）
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=password
MYSQL_DB=purchase_review

# 資料庫連接池設置
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# 資料庫重試設置
DB_RETRY_ATTEMPTS=3
DB_RETRY_DELAY=1

# Redis 設置
REDIS_URL=redis://localhost:6379/0

# 文件存儲設置
UPLOAD_DIR=./uploads
TEMP_DIR=./temp
MAX_FILE_SIZE=52428800  # 50MB
ALLOWED_FILE_TYPES=[".pdf", ".doc", ".docx"]

# PDF 解析設置
PDF_PARSE_TIMEOUT=300
OCR_LANGUAGE=chi_tra+eng

# AI 模型設置
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4-vision-preview
OPENAI_MAX_TOKENS=4000

# GraphRAG 設置
GRAPHRAG_ENABLED=true
GRAPHRAG_MODEL_PATH=./models/graphrag

# 日誌設置
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log

# Celery 設置
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# 監控設置
ENABLE_METRICS=true
METRICS_PORT=9090
