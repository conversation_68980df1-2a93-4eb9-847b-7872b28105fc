"""
分析結果相關的數據庫模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey, Enum as SQL<PERSON><PERSON>, JSON, Float
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from enum import Enum
from app.core.database import Base


class ResultType(str, Enum):
    """結果類型枚舉"""
    SUMMARY = "summary"             # 摘要
    ANALYSIS = "analysis"           # 分析
    EXTRACTION = "extraction"       # 提取
    CLASSIFICATION = "classification" # 分類
    RECOMMENDATION = "recommendation" # 建議


class ResultStatus(str, Enum):
    """結果狀態枚舉"""
    DRAFT = "draft"                 # 草稿
    PROCESSING = "processing"       # 處理中
    COMPLETED = "completed"         # 已完成
    REVIEWED = "reviewed"           # 已審核
    PUBLISHED = "published"         # 已發布
    ARCHIVED = "archived"           # 已歸檔


class ConfidenceLevel(str, Enum):
    """信心度等級枚舉"""
    VERY_LOW = "very_low"          # 很低 (0-20)
    LOW = "low"                    # 低 (21-40)
    MEDIUM = "medium"              # 中等 (41-60)
    HIGH = "high"                  # 高 (61-80)
    VERY_HIGH = "very_high"        # 很高 (81-100)


class AnalysisResult(Base):
    """分析結果模型"""

    __tablename__ = "analysis_results"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True)
    result_id = Column(String(36), unique=True, index=True, nullable=False, comment="結果唯一標識")
    
    # 關聯關係
    purchase_id = Column(String(36), ForeignKey("purchases.purchase_id"), nullable=False, comment="關聯的購案ID")
    task_id = Column(String(36), ForeignKey("analysis_tasks.task_id"), nullable=True, comment="關聯的任務ID")
    
    # 結果基本信息
    title = Column(String(200), nullable=False, comment="結果標題")
    description = Column(Text, nullable=True, comment="結果描述")
    result_type = Column(
        SQLEnum(ResultType), 
        nullable=False, 
        default=ResultType.ANALYSIS,
        comment="結果類型"
    )
    status = Column(
        SQLEnum(ResultStatus), 
        nullable=False, 
        default=ResultStatus.DRAFT,
        comment="結果狀態"
    )
    
    # 分析內容
    summary = Column(Text, nullable=True, comment="分析摘要")
    content = Column(Text, nullable=True, comment="詳細內容")
    key_findings = Column(JSON, nullable=True, comment="關鍵發現")
    recommendations = Column(JSON, nullable=True, comment="建議事項")
    
    # 信心度和評分
    confidence_score = Column(Float, nullable=True, comment="整體信心度（0-100）")
    confidence_level = Column(
        SQLEnum(ConfidenceLevel), 
        nullable=True,
        comment="信心度等級"
    )
    quality_score = Column(Float, nullable=True, comment="質量評分（0-100）")
    relevance_score = Column(Float, nullable=True, comment="相關性評分（0-100）")
    
    # 實體和關係
    entities = Column(JSON, nullable=True, comment="識別的實體")
    relationships = Column(JSON, nullable=True, comment="實體關係")
    keywords = Column(JSON, nullable=True, comment="關鍵詞")
    topics = Column(JSON, nullable=True, comment="主題標籤")
    
    # 統計信息
    word_count = Column(Integer, default=0, comment="字數統計")
    sentence_count = Column(Integer, default=0, comment="句子數量")
    paragraph_count = Column(Integer, default=0, comment="段落數量")
    page_count = Column(Integer, default=0, comment="頁面數量")
    
    # 分析時間
    analysis_start_time = Column(DateTime(timezone=True), nullable=True, comment="分析開始時間")
    analysis_end_time = Column(DateTime(timezone=True), nullable=True, comment="分析結束時間")
    analysis_duration = Column(Integer, nullable=True, comment="分析耗時（秒）")
    
    # 模型和方法
    analysis_model = Column(String(100), nullable=True, comment="使用的分析模型")
    analysis_method = Column(String(50), nullable=True, comment="分析方法")
    model_version = Column(String(20), nullable=True, comment="模型版本")
    parameters = Column(JSON, nullable=True, comment="分析參數")
    
    # 文件相關
    source_files = Column(JSON, nullable=True, comment="源文件列表")
    output_files = Column(JSON, nullable=True, comment="輸出文件列表")
    attachments = Column(JSON, nullable=True, comment="附件列表")
    
    # 相關文檔
    related_documents = Column(JSON, nullable=True, comment="相關文檔")
    similar_results = Column(JSON, nullable=True, comment="相似結果")
    references = Column(JSON, nullable=True, comment="參考資料")
    
    # 驗證和審核
    is_verified = Column(Boolean, default=False, comment="是否已驗證")
    verified_by = Column(String(100), nullable=True, comment="驗證者")
    verified_time = Column(DateTime(timezone=True), nullable=True, comment="驗證時間")
    verification_notes = Column(Text, nullable=True, comment="驗證備註")
    
    # 版本控制
    version = Column(String(20), default="1.0.0", comment="結果版本")
    parent_result_id = Column(String(36), nullable=True, comment="父結果ID")
    is_latest = Column(Boolean, default=True, comment="是否為最新版本")
    
    # 使用統計
    view_count = Column(Integer, default=0, comment="查看次數")
    download_count = Column(Integer, default=0, comment="下載次數")
    share_count = Column(Integer, default=0, comment="分享次數")
    last_accessed = Column(DateTime(timezone=True), nullable=True, comment="最後訪問時間")
    
    # 評價和反饋
    rating = Column(Float, nullable=True, comment="用戶評分（1-5）")
    feedback_count = Column(Integer, default=0, comment="反饋數量")
    feedback_summary = Column(JSON, nullable=True, comment="反饋摘要")
    
    # 標籤和分類
    tags = Column(JSON, nullable=True, comment="標籤")
    categories = Column(JSON, nullable=True, comment="分類")
    priority = Column(String(20), default="normal", comment="優先級")
    
    # 元數據
    extra_metadata = Column(JSON, nullable=True, comment="額外元數據")
    custom_fields = Column(JSON, nullable=True, comment="自定義字段")
    
    # 時間戳
    created_time = Column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="創建時間"
    )
    updated_time = Column(
        DateTime(timezone=True), 
        onupdate=func.now(),
        comment="更新時間"
    )
    published_time = Column(DateTime(timezone=True), nullable=True, comment="發布時間")
    
    # 軟刪除
    is_deleted = Column(Boolean, default=False, comment="是否已刪除")
    deleted_time = Column(DateTime(timezone=True), nullable=True, comment="刪除時間")

    # 關聯關係
    purchase = relationship("Purchase", back_populates="analysis_results")
    analysis_task = relationship("AnalysisTask", back_populates="analysis_results")

    def __repr__(self):
        return f"<AnalysisResult(id={self.id}, title='{self.title}', type='{self.result_type}')>"

    @property
    def is_completed(self) -> bool:
        """是否已完成"""
        return self.status == ResultStatus.COMPLETED

    @property
    def is_published(self) -> bool:
        """是否已發布"""
        return self.status == ResultStatus.PUBLISHED

    @property
    def analysis_time_formatted(self) -> str:
        """格式化的分析時間"""
        if not self.analysis_duration:
            return "未知"
        
        duration = self.analysis_duration
        if duration < 60:
            return f"{duration}秒"
        elif duration < 3600:
            minutes = duration // 60
            seconds = duration % 60
            return f"{minutes}分{seconds}秒"
        else:
            hours = duration // 3600
            minutes = (duration % 3600) // 60
            return f"{hours}小時{minutes}分鐘"

    @property
    def confidence_level_text(self) -> str:
        """信心度等級文本"""
        if not self.confidence_score:
            return "未知"
        
        score = self.confidence_score
        if score <= 20:
            return "很低"
        elif score <= 40:
            return "低"
        elif score <= 60:
            return "中等"
        elif score <= 80:
            return "高"
        else:
            return "很高"

    def start_analysis(self):
        """開始分析"""
        self.status = ResultStatus.PROCESSING
        self.analysis_start_time = func.now()

    def complete_analysis(self, confidence_score: float = None):
        """完成分析"""
        self.status = ResultStatus.COMPLETED
        self.analysis_end_time = func.now()
        if confidence_score is not None:
            self.confidence_score = confidence_score
            self.confidence_level = self._get_confidence_level(confidence_score)
        
        # 計算分析耗時
        if self.analysis_start_time and self.analysis_end_time:
            delta = self.analysis_end_time - self.analysis_start_time
            self.analysis_duration = int(delta.total_seconds())

    def _get_confidence_level(self, score: float) -> ConfidenceLevel:
        """根據分數獲取信心度等級"""
        if score <= 20:
            return ConfidenceLevel.VERY_LOW
        elif score <= 40:
            return ConfidenceLevel.LOW
        elif score <= 60:
            return ConfidenceLevel.MEDIUM
        elif score <= 80:
            return ConfidenceLevel.HIGH
        else:
            return ConfidenceLevel.VERY_HIGH

    def publish(self):
        """發布結果"""
        self.status = ResultStatus.PUBLISHED
        self.published_time = func.now()

    def archive(self):
        """歸檔結果"""
        self.status = ResultStatus.ARCHIVED
        self.is_latest = False

    def verify(self, verifier: str, notes: str = None):
        """驗證結果"""
        self.is_verified = True
        self.verified_by = verifier
        self.verified_time = func.now()
        if notes:
            self.verification_notes = notes

    def record_access(self):
        """記錄訪問"""
        self.view_count += 1
        self.last_accessed = func.now()

    def record_download(self):
        """記錄下載"""
        self.download_count += 1

    def record_share(self):
        """記錄分享"""
        self.share_count += 1

    def add_feedback(self, rating: float = None, feedback: dict = None):
        """添加反饋"""
        if rating is not None:
            if self.rating is None:
                self.rating = rating
                self.feedback_count = 1
            else:
                # 計算平均評分
                total_rating = self.rating * self.feedback_count + rating
                self.feedback_count += 1
                self.rating = total_rating / self.feedback_count
        
        if feedback:
            if not self.feedback_summary:
                self.feedback_summary = []
            self.feedback_summary.append({
                "feedback": feedback,
                "timestamp": func.now().isoformat()
            })

    def soft_delete(self):
        """軟刪除"""
        self.is_deleted = True
        self.deleted_time = func.now()

    def to_dict(self) -> dict:
        """轉換為字典"""
        return {
            "id": self.id,
            "result_id": self.result_id,
            "purchase_id": self.purchase_id,
            "task_id": self.task_id,
            "title": self.title,
            "description": self.description,
            "result_type": self.result_type.value if self.result_type else None,
            "status": self.status.value if self.status else None,
            "summary": self.summary,
            "content": self.content,
            "key_findings": self.key_findings,
            "recommendations": self.recommendations,
            "confidence_score": self.confidence_score,
            "confidence_level": self.confidence_level.value if self.confidence_level else None,
            "confidence_level_text": self.confidence_level_text,
            "quality_score": self.quality_score,
            "relevance_score": self.relevance_score,
            "entities": self.entities,
            "relationships": self.relationships,
            "keywords": self.keywords,
            "topics": self.topics,
            "word_count": self.word_count,
            "sentence_count": self.sentence_count,
            "paragraph_count": self.paragraph_count,
            "page_count": self.page_count,
            "analysis_start_time": self.analysis_start_time.isoformat() if self.analysis_start_time else None,
            "analysis_end_time": self.analysis_end_time.isoformat() if self.analysis_end_time else None,
            "analysis_duration": self.analysis_duration,
            "analysis_time_formatted": self.analysis_time_formatted,
            "analysis_model": self.analysis_model,
            "analysis_method": self.analysis_method,
            "model_version": self.model_version,
            "parameters": self.parameters,
            "source_files": self.source_files,
            "output_files": self.output_files,
            "related_documents": self.related_documents,
            "is_verified": self.is_verified,
            "verified_by": self.verified_by,
            "verified_time": self.verified_time.isoformat() if self.verified_time else None,
            "version": self.version,
            "is_latest": self.is_latest,
            "view_count": self.view_count,
            "download_count": self.download_count,
            "share_count": self.share_count,
            "last_accessed": self.last_accessed.isoformat() if self.last_accessed else None,
            "rating": self.rating,
            "feedback_count": self.feedback_count,
            "tags": self.tags,
            "categories": self.categories,
            "priority": self.priority,
            "extra_metadata": self.extra_metadata,
            "created_time": self.created_time.isoformat() if self.created_time else None,
            "updated_time": self.updated_time.isoformat() if self.updated_time else None,
            "published_time": self.published_time.isoformat() if self.published_time else None,
            "is_deleted": self.is_deleted,
            "is_completed": self.is_completed,
            "is_published": self.is_published
        }
