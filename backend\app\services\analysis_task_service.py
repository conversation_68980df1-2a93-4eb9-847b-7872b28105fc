"""
分析任務管理服務
"""

import uuid
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc

from app.models.analysis_task import AnalysisTask, TaskStatus, TaskPriority, TaskType
from app.models.purchase import Purchase
from app.core.database import get_db
import logging

logger = logging.getLogger(__name__)


class AnalysisTaskService:
    """分析任務管理服務類"""

    def __init__(self, db: Session):
        self.db = db

    def create_task(
        self,
        purchase_id: str,
        task_name: str,
        task_type: TaskType = TaskType.ANALYSIS,
        description: Optional[str] = None,
        file_id: Optional[str] = None,
        priority: TaskPriority = TaskPriority.NORMAL,
        config: Optional[Dict[str, Any]] = None,
        input_params: Optional[Dict[str, Any]] = None,
        scheduled_time: Optional[datetime] = None,
        depends_on: Optional[List[str]] = None,
        parent_task_id: Optional[str] = None,
        estimated_duration: Optional[int] = None
    ) -> AnalysisTask:
        """創建新的分析任務"""
        
        task_id = str(uuid.uuid4())
        
        task = AnalysisTask(
            task_id=task_id,
            purchase_id=purchase_id,
            file_id=file_id,
            task_type=task_type,
            task_name=task_name,
            description=description,
            priority=priority,
            config=config,
            input_params=input_params,
            scheduled_time=scheduled_time,
            depends_on=depends_on,
            parent_task_id=parent_task_id,
            estimated_duration=estimated_duration
        )
        
        self.db.add(task)
        self.db.commit()
        self.db.refresh(task)
        
        logger.info(f"創建分析任務成功: {task_id}")
        return task

    def get_task(self, task_id: str) -> Optional[AnalysisTask]:
        """根據ID獲取任務"""
        task = self.db.query(AnalysisTask).filter(
            and_(
                AnalysisTask.task_id == task_id,
                AnalysisTask.is_deleted == False
            )
        ).first()

        # 刷新任務狀態以獲取最新數據
        if task:
            self.db.refresh(task)

        return task

    def get_tasks(
        self,
        skip: int = 0,
        limit: int = 100,
        purchase_id: Optional[str] = None,
        status: Optional[TaskStatus] = None,
        task_type: Optional[TaskType] = None,
        priority: Optional[TaskPriority] = None,
        file_id: Optional[str] = None,
        order_by: str = "created_time",
        order_desc: bool = True
    ) -> List[AnalysisTask]:
        """獲取任務列表"""

        query = self.db.query(AnalysisTask).filter(AnalysisTask.is_deleted == False)

        # 購案篩選
        if purchase_id:
            query = query.filter(AnalysisTask.purchase_id == purchase_id)

        # 狀態篩選
        if status:
            query = query.filter(AnalysisTask.status == status)

        # 任務類型篩選
        if task_type:
            query = query.filter(AnalysisTask.task_type == task_type)

        # 優先級篩選
        if priority:
            query = query.filter(AnalysisTask.priority == priority)

        # 文件ID篩選
        if file_id:
            query = query.filter(AnalysisTask.file_id == file_id)
        
        # 排序
        if hasattr(AnalysisTask, order_by):
            order_column = getattr(AnalysisTask, order_by)
            if order_desc:
                query = query.order_by(desc(order_column))
            else:
                query = query.order_by(asc(order_column))
        
        return query.offset(skip).limit(limit).all()

    def get_tasks_by_dependency(self, dependency_task_id: str) -> List[AnalysisTask]:
        """獲取依賴於指定任務的其他任務"""
        # 使用SQLite兼容的JSON查詢語法
        return self.db.query(AnalysisTask).filter(
            and_(
                AnalysisTask.depends_on.like(f'%"{dependency_task_id}"%'),
                AnalysisTask.status == TaskStatus.PENDING,
                AnalysisTask.is_deleted == False
            )
        ).all()

    def get_pending_tasks(self, limit: int = 50) -> List[AnalysisTask]:
        """獲取待執行的任務"""
        
        now = datetime.utcnow()
        
        return self.db.query(AnalysisTask).filter(
            and_(
                AnalysisTask.status == TaskStatus.PENDING,
                AnalysisTask.is_deleted == False,
                or_(
                    AnalysisTask.scheduled_time.is_(None),
                    AnalysisTask.scheduled_time <= now
                )
            )
        ).order_by(
            desc(AnalysisTask.priority),
            asc(AnalysisTask.created_time)
        ).limit(limit).all()

    def start_task(
        self,
        task_id: str,
        worker_id: Optional[str] = None,
        process_id: Optional[str] = None,
        execution_node: Optional[str] = None
    ) -> Optional[AnalysisTask]:
        """開始執行任務"""
        
        task = self.get_task(task_id)
        if not task or not task.status == TaskStatus.PENDING:
            return None
        
        task.start_task()
        if worker_id:
            task.worker_id = worker_id
        if process_id:
            task.process_id = process_id
        if execution_node:
            task.execution_node = execution_node
        
        self.db.commit()
        self.db.refresh(task)
        
        logger.info(f"開始執行任務: {task_id}")
        return task

    def complete_task(
        self,
        task_id: str,
        result_data: Optional[Dict[str, Any]] = None,
        result_path: Optional[str] = None,
        output_files: Optional[List[Dict[str, Any]]] = None
    ) -> Optional[AnalysisTask]:
        """完成任務"""

        task = self.get_task(task_id)
        if not task:
            logger.warning(f"任務不存在: {task_id}")
            return None

        # 允許從 PENDING 或 RUNNING 狀態完成任務
        if task.status not in [TaskStatus.PENDING, TaskStatus.RUNNING]:
            logger.warning(f"任務狀態不允許完成: {task_id}, 當前狀態: {task.status.value}")
            return None

        task.complete_task(result_data)
        if result_path:
            task.result_path = result_path
        if output_files:
            task.output_files = output_files

        self.db.commit()
        self.db.refresh(task)

        logger.info(f"完成任務: {task_id}")
        return task

    def fail_task(
        self,
        task_id: str,
        error_message: str,
        error_details: Optional[str] = None,
        error_code: Optional[str] = None
    ) -> Optional[AnalysisTask]:
        """任務失敗"""
        
        task = self.get_task(task_id)
        if not task:
            return None
        
        task.fail_task(error_message, error_details, error_code)
        self.db.commit()
        self.db.refresh(task)
        
        logger.info(f"任務失敗: {task_id}")
        return task

    def cancel_task(
        self,
        task_id: str,
        reason: Optional[str] = None
    ) -> Optional[AnalysisTask]:
        """取消任務"""
        
        task = self.get_task(task_id)
        if not task or not task.can_cancel:
            return None
        
        task.cancel_task(reason)
        self.db.commit()
        self.db.refresh(task)
        
        logger.info(f"取消任務: {task_id}")
        return task

    def pause_task(self, task_id: str) -> Optional[AnalysisTask]:
        """暫停任務"""
        
        task = self.get_task(task_id)
        if not task:
            return None
        
        task.pause_task()
        self.db.commit()
        self.db.refresh(task)
        
        logger.info(f"暫停任務: {task_id}")
        return task

    def resume_task(self, task_id: str) -> Optional[AnalysisTask]:
        """恢復任務"""
        
        task = self.get_task(task_id)
        if not task:
            return None
        
        task.resume_task()
        self.db.commit()
        self.db.refresh(task)
        
        logger.info(f"恢復任務: {task_id}")
        return task

    def update_progress(
        self,
        task_id: str,
        progress: int,
        step: Optional[str] = None,
        step_index: Optional[int] = None
    ) -> Optional[AnalysisTask]:
        """更新任務進度"""
        
        task = self.get_task(task_id)
        if not task:
            return None
        
        task.update_progress(progress, step, step_index)
        self.db.commit()
        self.db.refresh(task)
        
        return task

    def retry_task(self, task_id: str) -> Optional[AnalysisTask]:
        """重試失敗的任務"""
        
        task = self.get_task(task_id)
        if not task or not task.can_retry:
            return None
        
        # 重置任務狀態
        task.status = TaskStatus.PENDING
        task.progress = 0
        task.current_step = None
        task.current_step_index = 0
        task.start_time = None
        task.end_time = None
        task.duration = None
        task.error_message = None
        task.error_details = None
        task.error_code = None
        
        self.db.commit()
        self.db.refresh(task)
        
        logger.info(f"重試任務: {task_id}")
        return task

    def get_task_statistics(self) -> Dict[str, Any]:
        """獲取任務統計信息"""
        
        total_count = self.db.query(AnalysisTask).filter(AnalysisTask.is_deleted == False).count()
        
        status_counts = {}
        for status in TaskStatus:
            count = self.db.query(AnalysisTask).filter(
                and_(
                    AnalysisTask.status == status,
                    AnalysisTask.is_deleted == False
                )
            ).count()
            status_counts[status.value] = count
        
        type_counts = {}
        for task_type in TaskType:
            count = self.db.query(AnalysisTask).filter(
                and_(
                    AnalysisTask.task_type == task_type,
                    AnalysisTask.is_deleted == False
                )
            ).count()
            type_counts[task_type.value] = count
        
        priority_counts = {}
        for priority in TaskPriority:
            count = self.db.query(AnalysisTask).filter(
                and_(
                    AnalysisTask.priority == priority,
                    AnalysisTask.is_deleted == False
                )
            ).count()
            priority_counts[priority.value] = count
        
        return {
            "total_count": total_count,
            "status_counts": status_counts,
            "type_counts": type_counts,
            "priority_counts": priority_counts
        }

    def cleanup_old_tasks(self, days: int = 30) -> int:
        """清理舊任務"""
        
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        old_tasks = self.db.query(AnalysisTask).filter(
            and_(
                AnalysisTask.status.in_([TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]),
                AnalysisTask.created_time < cutoff_date,
                AnalysisTask.is_deleted == False
            )
        ).all()
        
        count = 0
        for task in old_tasks:
            task.soft_delete()
            count += 1
        
        self.db.commit()
        
        logger.info(f"清理了 {count} 個舊任務")
        return count

    def get_tasks_by_purchase_and_type(
        self,
        purchase_id: str,
        task_type: TaskType
    ) -> List[AnalysisTask]:
        """根據購案ID和任務類型獲取任務列表"""
        return self.db.query(AnalysisTask).filter(
            and_(
                AnalysisTask.purchase_id == purchase_id,
                AnalysisTask.task_type == task_type,
                AnalysisTask.is_deleted == False
            )
        ).order_by(desc(AnalysisTask.created_time)).all()


def get_analysis_task_service(db: Session = None) -> AnalysisTaskService:
    """獲取分析任務服務實例"""
    if db is None:
        db = next(get_db())
    return AnalysisTaskService(db)
